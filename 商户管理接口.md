# 商户管理接口文档

## 🔐 认证说明
所有商户管理接口都需要在请求头中携带JWT Token：
```
Authorization: Bearer {access_token}
```

## 📊 接口列表

### 单个操作接口
### 1. 获取商户列表
**接口地址：** `GET /merchant`

**权限要求：** `merchant:query`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认10，最大100 |
| name | string | 否 | 商户名称（模糊搜索） |
| status | int | 否 | 状态筛选（0禁用，1启用） |

**请求示例：**
```bash
GET /merchant?page=1&size=10&name=测试&status=1
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取商户列表成功",
    "data": {
        "records": [
            {
                "id": 1,
                "name": "测试商户",
                "merchant_no": "M2025073002163152",
                "secret_key": "58e0803684646f99fc29bc1f490f080e",
                "remark": "这是一个测试商户",
                "status": 1,
                "status_text": "启用",
                "created_at": "2025-01-30 02:16:31",
                "updated_at": "2025-01-30 02:16:31"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    },
    "timestamp": 1753812991000
}
```

---

### 2. 获取商户详情
**接口地址：** `GET /merchant/{id}`

**权限要求：** `merchant:query`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 商户ID |

**请求示例：**
```bash
GET /merchant/1
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取商户详情成功",
    "data": {
        "id": 1,
        "name": "测试商户",
        "merchant_no": "M2025073002163152",
        "secret_key": "58e0803684646f99fc29bc1f490f080e",
        "remark": "这是一个测试商户",
        "status": 1,
        "status_text": "启用",
        "created_at": "2025-01-30 02:16:31",
        "updated_at": "2025-01-30 02:16:31"
    },
    "timestamp": 1753812991000
}
```

---

### 3. 创建商户
**接口地址：** `POST /merchant`

**权限要求：** `merchant:add`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 商户名称（2-100字符，唯一） |
| remark | string | 否 | 备注（最大1000字符） |
| status | int | 否 | 状态（0禁用，1启用），默认1 |

**请求示例：**
```bash
POST /merchant
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "新测试商户",
    "remark": "这是一个新的测试商户",
    "status": 1
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "商户创建成功",
    "data": {
        "id": 2,
        "name": "新测试商户",
        "merchant_no": "M2025073002163168",
        "secret_key": "a1b2c3d4e5f6789012345678901234567",
        "remark": "这是一个新的测试商户",
        "status": 1,
        "status_text": "启用",
        "created_at": "2025-01-30 02:16:31",
        "updated_at": "2025-01-30 02:16:31"
    },
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 商户号（merchant_no）和商户密钥（secret_key）会自动生成
- 商户号格式：M + YYYYMMDD + HHMMSS + 随机数(2位)
- 商户密钥为32位随机字符串

---

### 4. 更新商户
**接口地址：** `PUT /merchant/{id}`

**权限要求：** `merchant:edit`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 商户ID |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 商户名称（2-100字符） |
| remark | string | 否 | 备注（最大1000字符） |
| status | int | 否 | 状态（0禁用，1启用） |

**请求示例：**
```bash
PUT /merchant/1
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "更新后的商户名称",
    "remark": "更新后的备注信息",
    "status": 0
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "商户更新成功",
    "data": {
        "id": 1,
        "name": "更新后的商户名称",
        "merchant_no": "M2025073002163152",
        "secret_key": "58e0803684646f99fc29bc1f490f080e",
        "remark": "更新后的备注信息",
        "status": 0,
        "status_text": "禁用",
        "created_at": "2025-01-30 02:16:31",
        "updated_at": "2025-01-30 02:20:15"
    },
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 商户号（merchant_no）和商户密钥（secret_key）不可修改
- 商户名称不能与其他商户重复

---

### 5. 删除商户
**接口地址：** `DELETE /merchant/{id}`

**权限要求：** `merchant:delete`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 商户ID |

**请求示例：**
```bash
DELETE /merchant/1
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "商户删除成功",
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 删除操作是真实删除，数据无法恢复
- 删除前请确保该商户没有关联的业务数据

---

### 6. 重新生成商户密钥
**接口地址：** `PUT /merchant/{id}/regenerate-key`

**权限要求：** `merchant:edit`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 商户ID |

**请求示例：**
```bash
PUT /merchant/1/regenerate-key
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "商户密钥重新生成成功",
    "data": {
        "id": 1,
        "name": "测试商户",
        "merchant_no": "M2025073002163152",
        "secret_key": "new_generated_secret_key_32_chars",
        "remark": "这是一个测试商户",
        "status": 1,
        "status_text": "启用",
        "created_at": "2025-01-30 02:16:31",
        "updated_at": "2025-01-30 02:25:30"
    },
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 重新生成密钥后，旧密钥立即失效
- 请及时更新相关系统中的密钥配置

---

### 批量操作接口
### 7. 批量删除商户
**接口地址：** `DELETE /merchant/batch`

**权限要求：** `merchant:delete`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 商户ID数组 |

**请求示例：**
```bash
DELETE /merchant/batch
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "ids": [1, 2, 3]
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "批量删除商户成功",
    "data": {
        "total": 3,
        "success": 2,
        "failed": 1,
        "success_ids": [1, 2],
        "failed_items": [
            {
                "id": 3,
                "error": "商户不存在"
            }
        ]
    },
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 批量删除操作是真实删除，数据无法恢复
- 返回详细的成功和失败信息
- 部分成功时仍返回200状态码

---

### 8. 批量更新商户状态
**接口地址：** `PUT /merchant/batch/status`

**权限要求：** `merchant:edit`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 商户ID数组 |
| status | int | 是 | 状态（0禁用，1启用） |

**请求示例：**
```bash
PUT /merchant/batch/status
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "ids": [1, 2, 3],
    "status": 0
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "批量禁用商户成功",
    "data": {
        "total": 3,
        "success": 3,
        "failed": 0,
        "success_ids": [1, 2, 3],
        "failed_items": []
    },
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 可以批量启用或禁用商户
- 返回详细的成功和失败信息

---

### 9. 批量重新生成商户密钥
**接口地址：** `PUT /merchant/batch/regenerate-key`

**权限要求：** `merchant:edit`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 商户ID数组 |

**请求示例：**
```bash
PUT /merchant/batch/regenerate-key
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "ids": [1, 2, 3]
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "批量重新生成密钥成功",
    "data": {
        "total": 3,
        "success": 3,
        "failed": 0,
        "success_ids": [1, 2, 3],
        "failed_items": []
    },
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 批量重新生成密钥后，旧密钥立即失效
- 请及时更新相关系统中的密钥配置
- 返回详细的成功和失败信息

---

## 🚨 错误响应

### 参数验证错误
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": ["商户名称不能为空"],
    "timestamp": 1753812991000
}
```

### 权限不足
```json
{
    "code": 403,
    "message": "权限不足",
    "timestamp": 1753812991000
}
```

### 商户不存在
```json
{
    "code": 404,
    "message": "商户不存在",
    "timestamp": 1753812991000
}
```

### 商户名称重复
```json
{
    "code": 409,
    "message": "商户名称已存在",
    "timestamp": 1753812991000
}
```

### 服务器错误
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "timestamp": 1753812991000
}
```

---

## 🔑 权限配置

需要在权限管理中配置以下权限：

| 权限代码 | 权限名称 | 说明 |
|----------|----------|------|
| `merchant:query` | 查询商户 | 获取商户列表和详情 |
| `merchant:add` | 新增商户 | 创建新商户 |
| `merchant:edit` | 编辑商户 | 更新商户信息和重新生成密钥 |
| `merchant:delete` | 删除商户 | 删除商户 |

---

## 📝 使用说明

### 单个操作
1. **商户号和密钥：** 创建商户时自动生成，不可手动指定
2. **唯一性：** 商户名称和商户号都具有唯一性约束
3. **状态管理：** 可通过status字段控制商户的启用/禁用状态
4. **搜索功能：** 支持按商户名称模糊搜索和状态筛选
5. **分页查询：** 列表接口支持分页，建议合理设置页面大小
6. **安全性：** 所有接口都需要认证和相应权限

### 批量操作
1. **批量删除：** 支持一次删除多个商户，操作不可逆
2. **批量状态更新：** 支持批量启用或禁用商户
3. **批量密钥重新生成：** 支持批量重新生成商户密钥
4. **部分成功处理：** 批量操作支持部分成功，会详细返回成功和失败的项目
5. **权限控制：** 批量操作需要相应的权限，与单个操作权限一致
6. **参数验证：** 批量操作会验证所有传入的ID，无效ID会被忽略

---

**文档更新时间：** 2025-01-30  
**接口版本：** v1.0.0
