# 用户管理接口文档

## 📖 概述

用户管理接口提供系统用户的完整生命周期管理，包括用户CRUD操作、角色分配、密码管理等功能。

## 🔐 认证说明
所有用户管理接口都需要在请求头中携带JWT Token：
```
Authorization: Bearer {access_token}
```

## 📋 接口列表

### 1. 获取用户列表
**接口地址：** `GET /user`

**权限要求：** `system:user.query`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认10，最大100 |
| username | string | 否 | 用户名（模糊搜索） |
| status | int | 否 | 状态筛选（0禁用，1启用） |
| roleId | int | 否 | 角色ID筛选 |

**请求示例：**
```bash
GET /user?page=1&size=10&username=admin&status=1&roleId=1
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取用户列表成功",
    "data": {
        "records": [
            {
                "id": 1,
                "username": "admin",
                "status": 1,
                "status_text": "启用",
                "last_login_time": "2025-01-30 10:30:00",
                "last_login_ip": "127.0.0.1",
                "login_count": 10,
                "remark": "系统管理员",
                "created_at": "2025-01-01 00:00:00",
                "updated_at": "2025-01-30 10:30:00",
                "roles": [
                    {
                        "id": 1,
                        "code": "admin",
                        "name": "管理员",
                        "description": "系统管理员"
                    }
                ]
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    },
    "timestamp": 1753812991000
}
```

---

### 2. 创建用户
**接口地址：** `POST /user`

**权限要求：** `system:user.add`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名（3-50字符，字母数字，唯一） |
| password | string | 是 | 密码（6-32字符） |
| roleIds | array | 否 | 角色ID数组 |
| status | int | 否 | 状态（0禁用，1启用），默认1 |

**请求示例：**
```bash
POST /user
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "username": "testuser",
    "password": "123456",
    "roleIds": [1, 2],
    "status": 1
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "用户创建成功",
    "data": {
        "id": 2,
        "username": "testuser",
        "status": 1,
        "status_text": "启用",
        "last_login_time": null,
        "last_login_ip": null,
        "login_count": 0,
        "remark": null,
        "created_at": "2025-01-30 10:35:00",
        "updated_at": "2025-01-30 10:35:00",
        "roles": [
            {
                "id": 1,
                "code": "admin",
                "name": "管理员",
                "description": "系统管理员"
            },
            {
                "id": 2,
                "code": "user",
                "name": "普通用户",
                "description": "普通用户"
            }
        ]
    },
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
// 用户名已存在
{
    "code": 409,
    "message": "用户名已存在",
    "timestamp": 1753812991000
}
```

---

### 3. 更新用户
**接口地址：** `PUT /user/{id}`

**权限要求：** `system:user.edit`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 用户ID |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleIds | array | 否 | 角色ID数组 |
| status | int | 否 | 状态（0禁用，1启用） |

**请求示例：**
```bash
PUT /user/2
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "roleIds": [2],
    "status": 0
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "用户更新成功",
    "data": {
        "id": 2,
        "username": "testuser",
        "status": 0,
        "status_text": "禁用",
        "last_login_time": null,
        "last_login_ip": null,
        "login_count": 0,
        "remark": null,
        "created_at": "2025-01-30 10:35:00",
        "updated_at": "2025-01-30 10:40:00",
        "roles": [
            {
                "id": 2,
                "code": "user",
                "name": "普通用户",
                "description": "普通用户"
            }
        ]
    },
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 用户名不可修改
- 可以单独更新状态或角色，也可以同时更新

---

### 4. 删除用户
**接口地址：** `DELETE /user/{id}`

**权限要求：** `system:user.delete`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 用户ID |

**请求示例：**
```bash
DELETE /user/2
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "用户删除成功",
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
// 不能删除自己
{
    "code": 400,
    "message": "不能删除自己",
    "timestamp": 1753812991000
}

// 用户不存在
{
    "code": 404,
    "message": "用户不存在",
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 删除操作是真实删除，数据无法恢复
- 不能删除当前登录的用户
- 删除用户时会同时删除用户的角色关联关系

---

### 5. 重置用户密码
**接口地址：** `PUT /user/{id}/password`

**权限要求：** `system:user.edit`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 用户ID |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| password | string | 是 | 新密码（6-32字符） |

**请求示例：**
```bash
PUT /user/2/password
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "password": "newpassword123"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "密码重置成功",
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 管理员可以重置任何用户的密码
- 重置后用户需要使用新密码登录
- 密码会自动使用BCrypt加密

---

### 6. 用户修改自己的密码
**接口地址：** `PUT /auth/password`

**权限要求：** 需要认证（无需特殊权限）

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| old_password | string | 是 | 旧密码（6-32字符） |
| new_password | string | 是 | 新密码（6-32字符） |
| confirm_password | string | 是 | 确认密码（必须与新密码一致） |

**请求示例：**
```bash
PUT /auth/password
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "old_password": "123456",
    "new_password": "newpassword123",
    "confirm_password": "newpassword123"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "密码修改成功",
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
// 旧密码错误
{
    "code": 400,
    "message": "旧密码错误",
    "timestamp": 1753812991000
}

// 新密码与旧密码相同
{
    "code": 400,
    "message": "新密码不能与旧密码相同",
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 用户只能修改自己的密码
- 必须提供正确的旧密码
- 新密码不能与旧密码相同

---

## 🚨 通用错误响应

### 参数验证错误
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": ["用户名不能为空"],
    "timestamp": 1753812991000
}
```

### 权限不足
```json
{
    "code": 403,
    "message": "权限不足",
    "timestamp": 1753812991000
}
```

### 用户不存在
```json
{
    "code": 404,
    "message": "用户不存在",
    "timestamp": 1753812991000
}
```

### 服务器错误
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "timestamp": 1753812991000
}
```

---

## 🔑 权限配置

需要在权限管理中配置以下权限：

| 权限代码 | 权限名称 | 说明 |
|----------|----------|------|
| `system:user.query` | 查询用户 | 获取用户列表和详情 |
| `system:user.add` | 新增用户 | 创建新用户 |
| `system:user.edit` | 编辑用户 | 更新用户信息和重置密码 |
| `system:user.delete` | 删除用户 | 删除用户 |

---

## 📝 使用说明

### 1. 用户状态管理
- **启用（1）：** 用户可以正常登录和使用系统
- **禁用（0）：** 用户无法登录，已登录的会话会失效

### 2. 角色分配
- 用户可以分配多个角色
- 用户的权限是所有角色权限的并集
- 更新角色时会覆盖原有的角色分配

### 3. 密码管理
- **管理员重置：** 管理员可以直接设置新密码，无需旧密码
- **用户自改：** 用户修改自己的密码需要验证旧密码
- 所有密码都使用BCrypt加密存储

### 4. 搜索和筛选
- **用户名搜索：** 支持模糊搜索
- **状态筛选：** 可按启用/禁用状态筛选
- **角色筛选：** 可按角色筛选用户

### 5. 分页查询
- 支持分页查询，建议合理设置页面大小
- 返回数据包含总数、当前页、页面大小等分页信息

---

**文档更新时间：** 2025-01-30  
**接口版本：** v1.0.0
