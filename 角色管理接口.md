# 角色管理接口文档

## 📖 概述

角色管理接口提供系统角色的完整生命周期管理，包括角色CRUD操作、权限分配等功能。角色是权限的集合，用户通过分配角色来获得相应的权限。

## 🔐 认证说明
所有角色管理接口都需要在请求头中携带JWT Token：
```
Authorization: Bearer {access_token}
```

## 📋 接口列表

### 1. 获取角色列表
**接口地址：** `GET /role`

**权限要求：** `system:role.query`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认10，最大100 |
| name | string | 否 | 角色名称或代码（模糊搜索） |
| status | int | 否 | 状态筛选（0禁用，1启用） |

**请求示例：**
```bash
GET /role?page=1&size=10&name=admin&status=1
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取角色列表成功",
    "data": {
        "records": [
            {
                "id": 1,
                "code": "admin",
                "name": "管理员",
                "description": "系统管理员角色",
                "sort": 0,
                "status": 1,
                "createTime": "2025-01-01 00:00:00",
                "permissions": [
                    {
                        "permissionId": "system:user",
                        "permissionName": "用户管理",
                        "permissionCode": "system:user",
                        "actionList": ["query", "add", "edit", "delete"],
                        "actionEntitySet": [
                            {"action": "query", "describe": "查询"},
                            {"action": "add", "describe": "新增"},
                            {"action": "edit", "describe": "编辑"},
                            {"action": "delete", "describe": "删除"}
                        ]
                    }
                ]
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    },
    "timestamp": 1753812991000
}
```

---

### 2. 创建角色
**接口地址：** `POST /role`

**权限要求：** `system:role.add`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 是 | 角色代码（2-50字符，字母数字，唯一） |
| name | string | 是 | 角色名称（1-50字符） |
| description | string | 否 | 角色描述（最大255字符） |
| status | int | 否 | 状态（0禁用，1启用），默认1 |
| permissionIds | array | 否 | 权限ID数组 |

**请求示例：**
```bash
POST /role
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "code": "editor",
    "name": "编辑员",
    "description": "内容编辑员角色",
    "status": 1,
    "permissionIds": ["system:user.query", "merchant:query"]
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "角色创建成功",
    "data": {
        "id": 2,
        "code": "editor",
        "name": "编辑员",
        "description": "内容编辑员角色",
        "sort": 0,
        "status": 1,
        "createTime": "2025-01-30 10:35:00",
        "permissions": [
            {
                "permissionId": "system:user.query",
                "permissionName": "查询用户",
                "permissionCode": "system:user.query",
                "actionList": [],
                "actionEntitySet": []
            },
            {
                "permissionId": "merchant:query",
                "permissionName": "查询商户",
                "permissionCode": "merchant:query",
                "actionList": [],
                "actionEntitySet": []
            }
        ]
    },
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
// 角色代码已存在
{
    "code": 409,
    "message": "角色代码已存在",
    "timestamp": 1753812991000
}
```

---

### 3. 更新角色
**接口地址：** `PUT /role/{id}`

**权限要求：** `system:role.edit`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 角色ID |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 角色名称（1-50字符） |
| description | string | 否 | 角色描述（最大255字符） |
| status | int | 否 | 状态（0禁用，1启用） |
| permissionIds | array | 否 | 权限ID数组 |

**请求示例：**
```bash
PUT /role/2
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "高级编辑员",
    "description": "高级内容编辑员角色",
    "status": 1,
    "permissionIds": ["system:user.query", "merchant:query", "merchant:add"]
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "角色更新成功",
    "data": {
        "id": 2,
        "code": "editor",
        "name": "高级编辑员",
        "description": "高级内容编辑员角色",
        "sort": 0,
        "status": 1,
        "createTime": "2025-01-30 10:35:00",
        "permissions": [
            {
                "permissionId": "system:user.query",
                "permissionName": "查询用户",
                "permissionCode": "system:user.query",
                "actionList": [],
                "actionEntitySet": []
            },
            {
                "permissionId": "merchant:query",
                "permissionName": "查询商户",
                "permissionCode": "merchant:query",
                "actionList": [],
                "actionEntitySet": []
            },
            {
                "permissionId": "merchant:add",
                "permissionName": "新增商户",
                "permissionCode": "merchant:add",
                "actionList": [],
                "actionEntitySet": []
            }
        ]
    },
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 角色代码不可修改
- 可以单独更新角色信息或权限，也可以同时更新

---

### 4. 删除角色
**接口地址：** `DELETE /role/{id}`

**权限要求：** `system:role.delete`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 角色ID |

**请求示例：**
```bash
DELETE /role/2
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "角色删除成功",
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
// 角色下还有用户
{
    "code": 400,
    "message": "该角色下还有用户，无法删除",
    "timestamp": 1753812991000
}

// 角色不存在
{
    "code": 404,
    "message": "角色不存在",
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 删除操作是真实删除，数据无法恢复
- 如果角色下还有用户，则无法删除
- 删除角色时会同时删除角色的权限关联关系

---

### 5. 获取角色权限
**接口地址：** `GET /role/{id}/permissions`

**权限要求：** `system:role.query`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 角色ID |

**请求示例：**
```bash
GET /role/1/permissions
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取角色权限成功",
    "data": [
        {
            "permissionId": "system:user",
            "permissionName": "用户管理",
            "permissionCode": "system:user",
            "actionList": ["query", "add", "edit", "delete"],
            "actionEntitySet": [
                {"action": "query", "describe": "查询"},
                {"action": "add", "describe": "新增"},
                {"action": "edit", "describe": "编辑"},
                {"action": "delete", "describe": "删除"}
            ]
        },
        {
            "permissionId": "merchant:query",
            "permissionName": "查询商户",
            "permissionCode": "merchant:query",
            "actionList": [],
            "actionEntitySet": []
        }
    ],
    "timestamp": 1753812991000
}
```

---

### 6. 设置角色权限
**接口地址：** `PUT /role/{id}/permissions`

**权限要求：** `system:role.edit`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 角色ID |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| permissionIds | array | 是 | 权限ID数组 |

**请求示例：**
```bash
PUT /role/2/permissions
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "permissionIds": [
        "system:user.query",
        "system:user.add",
        "merchant:query"
    ]
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "设置角色权限成功",
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 此操作会覆盖角色原有的所有权限
- 传入空数组会清除角色的所有权限
- 权限ID必须是有效的权限标识

---

## 🚨 通用错误响应

### 参数验证错误
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": ["角色名称不能为空"],
    "timestamp": 1753812991000
}
```

### 权限不足
```json
{
    "code": 403,
    "message": "权限不足",
    "timestamp": 1753812991000
}
```

### 角色不存在
```json
{
    "code": 404,
    "message": "角色不存在",
    "timestamp": 1753812991000
}
```

### 服务器错误
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "timestamp": 1753812991000
}
```

---

## 🔑 权限配置

需要在权限管理中配置以下权限：

| 权限代码 | 权限名称 | 说明 |
|----------|----------|------|
| `system:role.query` | 查询角色 | 获取角色列表、详情和权限 |
| `system:role.add` | 新增角色 | 创建新角色 |
| `system:role.edit` | 编辑角色 | 更新角色信息和设置权限 |
| `system:role.delete` | 删除角色 | 删除角色 |

---

## 📝 使用说明

### 1. 角色状态管理
- **启用（1）：** 角色可以正常分配给用户
- **禁用（0）：** 角色无法分配给新用户，已分配的用户权限会受影响

### 2. 权限分配
- 角色可以分配多个权限
- 用户通过角色获得权限，用户的最终权限是所有角色权限的并集
- 权限分配支持批量操作

### 3. 角色代码规范
- 角色代码用于程序中的角色识别
- 建议使用有意义的英文单词，如：admin、editor、viewer
- 角色代码创建后不可修改

### 4. 搜索和筛选
- **名称搜索：** 支持按角色名称或代码模糊搜索
- **状态筛选：** 可按启用/禁用状态筛选

### 5. 删除限制
- 如果角色下还有用户，则无法删除角色
- 建议先将用户转移到其他角色，再删除角色

---

**文档更新时间：** 2025-01-30  
**接口版本：** v1.0.0
