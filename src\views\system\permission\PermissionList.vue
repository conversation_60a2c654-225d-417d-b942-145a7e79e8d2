<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="权限名称">
              <a-input v-model="queryParam.name" placeholder="请输入权限名称"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="权限类型">
              <a-select v-model="queryParam.type" placeholder="请选择" allowClear>
                <a-select-option value="menu">菜单权限</a-select-option>
                <a-select-option value="button">按钮权限</a-select-option>
                <a-select-option value="api">API权限</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="状态">
              <a-select v-model="queryParam.status" placeholder="请选择" allowClear>
                <a-select-option :value="1">启用</a-select-option>
                <a-select-option :value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="handleAdd" v-action:add>新建权限</a-button>
      <a-button style="margin-left: 8px" icon="reload" @click="handleRefresh">刷新</a-button>
      <a-button style="margin-left: 8px" @click="expandAll">展开全部</a-button>
      <a-button style="margin-left: 8px" @click="collapseAll">收起全部</a-button>
    </div>

    <a-table
      ref="table"
      size="default"
      rowKey="id"
      :columns="columns"
      :data-source="dataSource"
      :pagination="false"
      :loading="loading"
      :expandedRowKeys="expandedRowKeys"
      :scroll="{ x: 1400 }"
      @expand="onExpand"
    >
      <span slot="type" slot-scope="text">
        <a-tag :color="getTypeColor(text)">
          {{ getTypeText(text) }}
        </a-tag>
      </span>

      <span slot="code" slot-scope="text">
        <a-tag color="purple">{{ text }}</a-tag>
      </span>

      <span slot="actions" slot-scope="text">
        <template v-if="text && text.length > 0">
          <a-tag v-for="action in text" :key="getActionKey(action)" color="blue" style="margin: 2px;">
            {{ getActionText(action) }}
          </a-tag>
        </template>
        <span v-else>-</span>
      </span>

      <span slot="hidden" slot-scope="text">
        <a-tag :color="text === 0 ? 'green' : 'orange'">
          {{ text === 0 ? '显示' : '隐藏' }}
        </a-tag>
      </span>

      <span slot="status" slot-scope="text">
        <a-badge :status="text === 1 ? 'success' : 'default'" :text="text === 1 ? '启用' : '禁用'" />
      </span>

      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleView(record)">查看</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)" v-action:edit>编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">
              更多 <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item key="1">
                <a @click="handleAddChild(record)" v-action:add>添加子权限</a>
              </a-menu-item>
              <a-menu-item key="2">
                <a @click="handleToggleStatus(record)">
                  {{ record.status === 1 ? '禁用' : '启用' }}
                </a>
              </a-menu-item>
              <a-menu-item key="3">
                <a @click="handleDelete(record)" v-action:delete style="color: #ff4d4f;">删除</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </template>
      </span>
    </a-table>

    <permission-modal ref="modal" @ok="handleOk"></permission-modal>

  </a-card>
</template>

<script>
import PermissionModal from './modules/PermissionModal'
import { getPermissionTree, deletePermission, updatePermission } from '@/api/permission'

export default {
  name: 'SystemPermissionList',
  components: {
    PermissionModal
  },
  data () {
    return {
      description: '权限管理：系统权限的层级管理，支持菜单权限和按钮权限的配置。',

      // 查询参数
      queryParam: {},
      // 表格数据
      dataSource: [],
      loading: false,
      expandedRowKeys: [],

      // 表头
      columns: [
        {
          title: '权限名称',
          dataIndex: 'name',
          key: 'name',
          width: 200,
          fixed: 'left',
          ellipsis: true
        },
        {
          title: '权限代码',
          dataIndex: 'code',
          key: 'code',
          scopedSlots: { customRender: 'code' },
          width: 150,
          ellipsis: true
        },
        {
          title: '权限类型',
          dataIndex: 'type',
          key: 'type',
          scopedSlots: { customRender: 'type' },
          width: 100
        },
        {
          title: '路由路径',
          dataIndex: 'path',
          key: 'path',
          width: 150,
          ellipsis: true
        },
        {
          title: '图标',
          dataIndex: 'icon',
          key: 'icon',
          width: 80
        },
        {
          title: '操作权限',
          dataIndex: 'actions',
          key: 'actions',
          scopedSlots: { customRender: 'actions' },
          width: 200,
          ellipsis: true
        },
        {
          title: '排序',
          dataIndex: 'sort',
          key: 'sort',
          width: 80
        },
        {
          title: '隐藏',
          dataIndex: 'hidden',
          key: 'hidden',
          scopedSlots: { customRender: 'hidden' },
          width: 80
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          scopedSlots: { customRender: 'status' },
          width: 80
        },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' },
          width: 200,
          fixed: 'right'
        }
      ]
    }
  },

  created () {
    this.loadData()
  },

  methods: {
    // 加载数据
    loadData () {
      this.loading = true
      console.log('开始加载权限树数据...')
      getPermissionTree().then(res => {
        console.log('权限树API完整响应:', res)
        console.log('响应数据类型:', typeof res)
        console.log('响应数据结构:', Object.keys(res))

        // 检查不同可能的数据位置
        if (res.data) {
          console.log('res.data:', res.data)
          console.log('res.data类型:', typeof res.data)
          console.log('res.data是否为数组:', Array.isArray(res.data))
          this.dataSource = res.data
        } else if (res.result) {
          console.log('res.result:', res.result)
          console.log('res.result类型:', typeof res.result)
          console.log('res.result是否为数组:', Array.isArray(res.result))
          this.dataSource = res.result
        } else {
          console.log('未找到数据，使用空数组')
          this.dataSource = []
        }

        console.log('最终设置的dataSource:', this.dataSource)
        console.log('dataSource长度:', this.dataSource.length)
        this.loading = false
      }).catch(err => {
        console.error('加载权限数据失败:', err)
        this.$message.error('加载权限数据失败: ' + (err.message || '未知错误'))
        this.loading = false
      })
    },

    // 搜索
    handleSearch () {
      this.loadData()
    },

    // 重置
    handleReset () {
      this.queryParam = {}
      this.loadData()
    },

    // 刷新
    handleRefresh () {
      this.loadData()
    },

    // 获取类型颜色
    getTypeColor (type) {
      const colorMap = {
        menu: 'blue',
        button: 'green',
        api: 'orange'
      }
      return colorMap[type] || 'default'
    },

    // 获取类型文本
    getTypeText (type) {
      const textMap = {
        menu: '菜单权限',
        button: '按钮权限',
        api: 'API权限'
      }
      return textMap[type] || type
    },

    // 获取操作文本
    getActionText (action) {
      // 兼容新旧格式
      if (typeof action === 'object' && action.describe) {
        return action.describe
      } else if (typeof action === 'object' && action.action) {
        return this.getActionDescribe(action.action)
      } else if (typeof action === 'string') {
        return this.getActionDescribe(action)
      }
      return action
    },

    // 获取操作描述
    getActionDescribe (actionCode) {
      const actionMap = {
        query: '查询',
        add: '新增',
        edit: '编辑',
        delete: '删除',
        export: '导出',
        import: '导入',
        permission: '权限设置',
        view: '查看'
      }
      return actionMap[actionCode] || actionCode
    },

    // 获取操作的key
    getActionKey (action) {
      if (typeof action === 'object' && action.action) {
        return action.action
      } else if (typeof action === 'string') {
        return action
      }
      return Math.random().toString(36).substr(2, 9)
    },

    // 展开全部
    expandAll () {
      const keys = []
      this.getAllKeys(this.dataSource, keys)
      this.expandedRowKeys = keys
    },

    // 收起全部
    collapseAll () {
      this.expandedRowKeys = []
    },

    // 获取所有节点的key
    getAllKeys (data, keys) {
      data.forEach(item => {
        keys.push(item.id)
        if (item.children && item.children.length > 0) {
          this.getAllKeys(item.children, keys)
        }
      })
    },

    // 展开/收起节点
    onExpand (expanded, record) {
      if (expanded) {
        this.expandedRowKeys.push(record.id)
      } else {
        const index = this.expandedRowKeys.indexOf(record.id)
        if (index > -1) {
          this.expandedRowKeys.splice(index, 1)
        }
      }
    },

    // 新增权限
    handleAdd () {
      this.$refs.modal.add()
    },

    // 添加子权限
    handleAddChild (record) {
      this.$refs.modal.add(record.id)
    },

    // 查看权限
    handleView (record) {
      this.$refs.modal.view(record)
    },

    // 编辑权限
    handleEdit (record) {
      this.$refs.modal.edit(record)
    },

    // 删除权限
    handleDelete (record) {
      const that = this
      this.$confirm({
        title: '确认删除',
        content: `确认删除权限 ${record.name} 吗？`,
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        onOk () {
          return deletePermission(record.id).then(() => {
            that.$message.success('删除成功')
            that.loadData()
          }).catch(err => {
            that.$message.error('删除失败：' + (err.message || '未知错误'))
          })
        }
      })
    },

    // 切换状态
    handleToggleStatus (record) {
      const status = record.status === 1 ? 0 : 1
      const statusText = status === 1 ? '启用' : '禁用'

      this.$confirm({
        title: '确认操作',
        content: `确认${statusText}权限 ${record.name} 吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          return updatePermission(record.id, { status }).then(() => {
            this.$message.success(`${statusText}成功`)
            this.loadData()
          }).catch(err => {
            this.$message.error(`${statusText}失败：` + (err.message || '未知错误'))
          })
        }
      })
    },

    // 处理模态框确认
    handleOk () {
      this.loadData()
    }
  }
}
</script>

<style scoped>
/* 移动端表格优化 */
@media (max-width: 768px) {
  .table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* 确保表格在小屏幕下可以滚动 */
  :deep(.ant-table-wrapper) {
    overflow-x: auto;
  }

  :deep(.ant-table) {
    min-width: 1200px;
  }

  /* 优化固定列的显示 */
  :deep(.ant-table-fixed-left) {
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }

  :deep(.ant-table-fixed-right) {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  }

  /* 优化操作按钮在移动端的显示 */
  .table-operator {
    .ant-btn {
      margin-bottom: 8px;
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  :deep(.ant-table) {
    font-size: 12px;
    min-width: 1000px;
  }

  :deep(.ant-btn) {
    padding: 0 8px;
    font-size: 12px;
  }

  :deep(.ant-tag) {
    font-size: 11px;
    padding: 0 4px;
  }

  .table-operator {
    .ant-btn {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}
</style>
