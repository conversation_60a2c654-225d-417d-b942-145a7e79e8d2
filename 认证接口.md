# 认证接口文档

## 📖 概述

认证接口提供用户登录、退出、token刷新和用户信息获取等功能，采用JWT（JSON Web Token）进行身份认证。

## 🔐 认证机制

### JWT Token说明
- **Access Token：** 用于API访问的短期令牌，有效期2小时
- **Refresh Token：** 用于刷新Access Token的长期令牌，有效期7天
- **Token格式：** Bearer Token，需在请求头中携带

### 请求头格式
```
Authorization: Bearer {access_token}
```

支持的请求头格式：
- `Authorization: Bearer {token}` (标准格式)
- `ACCESS_TOKEN: {token}` (Ant Design Vue Pro格式)
- `Access-Token: {token}` (备用格式)
- `X-Access-Token: {token}` (备用格式)

## 📋 接口列表

### 1. 用户登录
**接口地址：** `POST /auth/login`

**权限要求：** 无需认证

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名（3-50字符，字母数字） |
| password | string | 是 | 密码（6-32字符） |

**请求示例：**
```bash
POST /auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "123456"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": {
            "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
            "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
            "expiresIn": 7200
        },
        "userInfo": {
            "id": 1,
            "username": "admin",
            "status": 1,
            "last_login_time": "2025-01-30 10:30:00",
            "last_login_ip": "127.0.0.1",
            "login_count": 10,
            "created_at": "2025-01-01 00:00:00",
            "updated_at": "2025-01-30 10:30:00"
        }
    },
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
// 用户不存在
{
    "code": 40001,
    "message": "用户不存在或已被禁用",
    "timestamp": 1753812991000
}

// 密码错误
{
    "code": 40002,
    "message": "密码错误",
    "timestamp": 1753812991000
}

// 账户被禁用
{
    "code": 40003,
    "message": "账户已被禁用",
    "timestamp": 1753812991000
}
```

---

### 2. 获取用户信息
**接口地址：** `GET /auth/userinfo`

**权限要求：** 需要认证

**请求示例：**
```bash
GET /auth/userinfo
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
        "userInfo": {
            "id": 1,
            "username": "admin",
            "status": 1,
            "last_login_time": "2025-01-30 10:30:00",
            "last_login_ip": "127.0.0.1",
            "login_count": 10,
            "created_at": "2025-01-01 00:00:00",
            "updated_at": "2025-01-30 10:30:00"
        },
        "permissions": [
            "system:user.query",
            "system:user.add",
            "system:user.edit",
            "system:user.delete",
            "merchant:query",
            "merchant:add"
        ],
        "roles": [
            {
                "id": 1,
                "code": "admin",
                "name": "管理员",
                "description": "系统管理员"
            }
        ]
    },
    "timestamp": 1753812991000
}
```

---

### 3. 刷新Token
**接口地址：** `POST /auth/refresh`

**权限要求：** 无需认证

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| refresh_token | string | 是 | 刷新令牌 |

**请求示例：**
```bash
POST /auth/refresh
Content-Type: application/json

{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expiresIn": 7200,
        "tokenType": "Bearer"
    },
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
{
    "code": 401,
    "message": "刷新令牌无效或已过期",
    "data": {
        "result": {
            "isLogin": false
        }
    },
    "timestamp": 1753812991000
}
```

---

### 4. 退出登录
**接口地址：** `POST /auth/logout`

**权限要求：** 无需认证（允许token失效时访问）

**请求示例：**
```bash
POST /auth/logout
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "退出登录成功",
    "timestamp": 1753812991000
}
```

**说明：**
- 退出登录会注销用户的刷新令牌
- 即使access_token已过期也可以调用此接口
- 退出后需要重新登录获取新的token

---

### 5. 修改密码
**接口地址：** `PUT /auth/password`

**权限要求：** 需要认证

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| old_password | string | 是 | 旧密码（6-32字符） |
| new_password | string | 是 | 新密码（6-32字符） |
| confirm_password | string | 是 | 确认密码（必须与新密码一致） |

**请求示例：**
```bash
PUT /auth/password
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "old_password": "123456",
    "new_password": "newpassword123",
    "confirm_password": "newpassword123"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "密码修改成功",
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
// 旧密码错误
{
    "code": 400,
    "message": "旧密码错误",
    "timestamp": 1753812991000
}

// 新密码与旧密码相同
{
    "code": 400,
    "message": "新密码不能与旧密码相同",
    "timestamp": 1753812991000
}
```

---

## 🚨 通用错误响应

### 参数验证错误
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": ["用户名不能为空"],
    "timestamp": 1753812991000
}
```

### 认证失败
```json
{
    "code": 401,
    "message": "请提供访问令牌",
    "data": {
        "result": {
            "isLogin": false
        }
    },
    "timestamp": 1753812991000
}
```

### Token无效或过期
```json
{
    "code": 401,
    "message": "访问令牌无效或已过期",
    "data": {
        "result": {
            "isLogin": false
        }
    },
    "timestamp": 1753812991000
}
```

### 服务器错误
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "timestamp": 1753812991000
}
```

---

## 🔧 使用说明

### 1. 登录流程
1. 调用登录接口获取access_token和refresh_token
2. 将access_token保存到本地存储
3. 在后续API请求中携带access_token

### 2. Token刷新流程
1. 当access_token过期时，使用refresh_token刷新
2. 获取新的access_token和refresh_token
3. 更新本地存储的token

### 3. 退出流程
1. 调用退出接口注销refresh_token
2. 清除本地存储的所有token
3. 跳转到登录页面

### 4. 安全建议
- 定期刷新token，避免长时间使用同一token
- 在安全的地方存储refresh_token
- 检测到token异常时及时退出登录
- 使用HTTPS传输敏感信息

---

**文档更新时间：** 2025-01-30  
**接口版本：** v1.0.0
