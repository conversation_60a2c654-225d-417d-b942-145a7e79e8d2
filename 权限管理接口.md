# 权限管理接口文档

## 📖 概述

权限管理接口提供系统权限的完整管理功能，包括权限CRUD操作、权限树结构等。权限是系统中最小的访问控制单元，支持菜单权限、按钮权限和API权限。

## � 重要更新（2025-01-30）

**权限表结构优化：**
- **ID字段**：改为自增整数主键，用于数据库关联
- **权限代码**：保留code字段，用于业务逻辑和权限验证
- **父权限关系**：使用parent_code指定，系统自动转换为parent_id
- **接口简化**：创建权限时只需提供权限代码，系统自动生成ID

**数据库结构：**
```sql
CREATE TABLE admin_permissions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,  -- 自增主键，用于关联
    code VARCHAR(100) UNIQUE NOT NULL,           -- 权限代码，用于验证
    name VARCHAR(100) NOT NULL,                  -- 权限名称
    type VARCHAR(20) DEFAULT 'menu',             -- 权限类型
    parent_id INT UNSIGNED,                      -- 父权限ID（整数）
    -- 其他字段...
);
```

**权限类型：**
- `menu`: 菜单权限
- `button`: 按钮权限
- `api`: API接口权限

**权限代码规范：**
- 模块权限：`模块名`，如：`system`
- 功能权限：`模块名:功能名`，如：`system:user`
- 操作权限：`模块名:功能名.操作`，如：`system:user.query`

## �🔐 认证说明
所有权限管理接口都需要在请求头中携带JWT Token：
```
Authorization: Bearer {access_token}
```

## 📋 接口列表

### 1. 获取权限树
**接口地址：** `GET /permission/tree`

**权限要求：** `system:permission.query`

**请求示例：**
```bash
GET /permission/tree
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取权限树成功",
    "data": [
        {
            "id": 4,
            "name": "系统管理",
            "code": "system",
            "type": "menu",
            "parent_id": null,
            "path": "/system",
            "icon": "setting",
            "component": null,
            "redirect": null,
            "sort": 1,
            "status": 1,
            "hidden": 0,
            "actions": ["query", "add", "edit", "delete"],
            "description": "系统管理模块",
            "children": [
                {
                    "id": 5,
                    "name": "用户管理",
                    "code": "system:user",
                    "type": "menu",
                    "parent_id": 4,
                    "path": "/system/user",
                    "icon": "user",
                    "component": "system/user/index",
                    "redirect": null,
                    "sort": 1,
                    "status": 1,
                    "hidden": 0,
                    "actions": ["query", "add", "edit", "delete"],
                    "description": "用户管理页面",
                    "children": [
                        {
                            "id": 6,
                            "name": "查询用户",
                            "code": "system:user.query",
                            "type": "api",
                            "parent_id": 5,
                            "path": null,
                            "icon": null,
                            "component": null,
                            "redirect": null,
                            "sort": 1,
                            "status": 1,
                            "hidden": 0,
                            "actions": [],
                            "description": "查询用户权限",
                            "children": []
                        }
                    ]
                }
            ]
        }
    ],
    "timestamp": 1753812991000
}
```

---

### 2. 获取权限列表
**接口地址：** `GET /permission`

**权限要求：** `system:permission.query`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认10，最大100 |
| name | string | 否 | 权限名称（模糊搜索） |
| type | string | 否 | 权限类型（menu/button/api） |
| status | int | 否 | 状态筛选（0禁用，1启用） |

**请求示例：**
```bash
GET /permission?page=1&size=10&name=用户&type=menu&status=1
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取权限列表成功",
    "data": {
        "records": [
            {
                "id": "system:user",
                "name": "用户管理",
                "code": "system:user",
                "type": "menu",
                "parent_id": "system",
                "path": "/system/user",
                "icon": "user",
                "component": "system/user/index",
                "redirect": null,
                "sort": 1,
                "status": 1,
                "hidden": 0,
                "actions": ["query", "add", "edit", "delete"],
                "description": "用户管理页面",
                "created_at": "2025-01-01 00:00:00",
                "updated_at": "2025-01-30 10:30:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    },
    "timestamp": 1753812991000
}
```

---

### 3. 创建权限
**接口地址：** `POST /permission`

**权限要求：** `system:permission.add`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 权限名称（1-100字符） |
| code | string | 是 | 权限代码（1-100字符，支持字母数字冒号点号下划线横线，唯一） |
| type | string | 是 | 权限类型（menu/button/api） |
| parent_code | string | 否 | 父权限代码 |
| path | string | 否 | 路由路径（最大255字符） |
| icon | string | 否 | 图标（最大100字符） |
| component | string | 否 | 组件路径（最大255字符） |
| redirect | string | 否 | 重定向路径（最大255字符） |
| sort | int | 否 | 排序（默认0） |
| status | int | 否 | 状态（0禁用，1启用），默认1 |
| hidden | int | 否 | 是否隐藏（0显示，1隐藏），默认0 |
| actions | array | 否 | 操作列表 |
| description | string | 否 | 描述（最大500字符） |

**请求示例：**
```bash
POST /permission
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "角色管理",
    "code": "system:role",
    "type": "menu",
    "parent_code": "system",
    "path": "/system/role",
    "icon": "team",
    "component": "system/role/index",
    "sort": 2,
    "status": 1,
    "hidden": 0,
    "actions": ["query", "add", "edit", "delete"],
    "description": "角色管理页面"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "权限创建成功",
    "data": {
        "id": 7,
        "name": "角色管理",
        "code": "system:role",
        "type": "menu",
        "parent_id": 4,
        "path": "/system/role",
        "icon": "team",
        "component": "system/role/index",
        "redirect": null,
        "sort": 2,
        "status": 1,
        "hidden": 0,
        "actions": ["query", "add", "edit", "delete"],
        "description": "角色管理页面",
        "created_at": "2025-01-30 10:35:00",
        "updated_at": "2025-01-30 10:35:00"
    },
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
// 权限代码已存在
{
    "code": 409,
    "message": "权限代码已存在",
    "timestamp": 1753812991000
}
```

**特殊说明：**
- **权限ID**：由系统自动生成（自增整数），用于数据库关联，无需手动指定
- **权限代码**：用于程序中的权限验证，必须唯一，支持字母、数字、冒号、点号、下划线和横线
- **父权限关系**：使用parent_code指定父权限代码，系统会自动转换为对应的parent_id
- **接口简化**：相比旧版本，创建权限时不再需要提供id参数，大大简化了使用复杂度

**使用示例：**
```bash
# 创建顶级模块权限
{
    "name": "商户管理",
    "code": "merchant",
    "type": "menu"
}

# 创建子模块权限
{
    "name": "商户列表",
    "code": "merchant:list",
    "type": "menu",
    "parent_code": "merchant"
}

# 创建操作权限
{
    "name": "查询商户",
    "code": "merchant:list.query",
    "type": "api",
    "parent_code": "merchant:list"
}
```

---

### 4. 更新权限
**接口地址：** `PUT /permission/{id}`

**权限要求：** `system:permission.edit`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 权限ID（自增整数） |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 权限名称（1-100字符） |
| code | string | 否 | 权限代码（1-100字符） |
| type | string | 否 | 权限类型（menu/button/api） |
| parent_code | string | 否 | 父权限代码 |
| path | string | 否 | 路由路径（最大255字符） |
| icon | string | 否 | 图标（最大100字符） |
| component | string | 否 | 组件路径（最大255字符） |
| redirect | string | 否 | 重定向路径（最大255字符） |
| sort | int | 否 | 排序 |
| status | int | 否 | 状态（0禁用，1启用） |
| hidden | int | 否 | 是否隐藏（0显示，1隐藏） |
| actions | array | 否 | 操作列表 |
| description | string | 否 | 描述（最大500字符） |

**请求示例：**
```bash
PUT /permission/1
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "角色权限管理",
    "description": "角色和权限管理页面",
    "sort": 3,
    "status": 1
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "权限更新成功",
    "data": {
        "id": "system:role",
        "name": "角色权限管理",
        "code": "system:role",
        "type": "menu",
        "parent_id": "system",
        "path": "/system/role",
        "icon": "team",
        "component": "system/role/index",
        "redirect": null,
        "sort": 3,
        "status": 1,
        "hidden": 0,
        "actions": ["query", "add", "edit", "delete"],
        "description": "角色和权限管理页面",
        "created_at": "2025-01-30 10:35:00",
        "updated_at": "2025-01-30 10:40:00"
    },
    "timestamp": 1753812991000
}
```

---

### 5. 删除权限
**接口地址：** `DELETE /permission/{id}`

**权限要求：** `system:permission.delete`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 权限ID（自增整数） |

**请求示例：**
```bash
DELETE /permission/1
Authorization: Bearer {access_token}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "权限删除成功",
    "timestamp": 1753812991000
}
```

**错误响应：**
```json
// 权限下还有子权限
{
    "code": 400,
    "message": "该权限下还有子权限，无法删除",
    "timestamp": 1753812991000
}

// 权限正在被角色使用
{
    "code": 400,
    "message": "该权限正在被角色使用，无法删除",
    "timestamp": 1753812991000
}
```

**特殊说明：**
- 删除操作是真实删除，数据无法恢复
- 如果权限下还有子权限，则无法删除
- 如果权限正在被角色使用，则无法删除

---

## 🎯 权限类型说明

### 1. 菜单权限 (menu)
- **用途：** 控制菜单的显示和访问
- **必填字段：** id, name, code, type, path, component
- **可选字段：** parent_id, icon, redirect, sort, hidden, actions

### 2. 按钮权限 (button)
- **用途：** 控制页面内按钮的显示和操作
- **必填字段：** id, name, code, type, parent_id
- **可选字段：** sort, hidden, actions

### 3. API权限 (api)
- **用途：** 控制API接口的访问权限
- **必填字段：** id, name, code, type
- **可选字段：** parent_id, sort, hidden

---

## 🚨 通用错误响应

### 参数验证错误
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": ["权限名称不能为空"],
    "timestamp": 1753812991000
}
```

### 权限不足
```json
{
    "code": 403,
    "message": "权限不足",
    "timestamp": 1753812991000
}
```

### 权限不存在
```json
{
    "code": 404,
    "message": "权限不存在",
    "timestamp": 1753812991000
}
```

### 服务器错误
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "timestamp": 1753812991000
}
```

---

## 🔑 权限配置

需要在权限管理中配置以下权限：

| 权限代码 | 权限名称 | 说明 |
|----------|----------|------|
| `system:permission.query` | 查询权限 | 获取权限列表、树结构和详情 |
| `system:permission.add` | 新增权限 | 创建新权限 |
| `system:permission.edit` | 编辑权限 | 更新权限信息 |
| `system:permission.delete` | 删除权限 | 删除权限 |

---

## 📝 使用说明

### 1. 权限层级结构
- 权限支持多级层级结构
- 通过parent_id建立父子关系
- 权限树的根节点parent_id为null

### 2. 权限代码规范
- 建议使用模块:功能.操作的格式，如：system:user.query
- 权限代码用于程序中的权限验证
- 权限代码必须唯一

### 3. 权限状态管理
- **启用（1）：** 权限可以正常分配和使用
- **禁用（0）：** 权限无法分配，已分配的会失效

### 4. 隐藏权限
- **显示（0）：** 权限在前端菜单中显示
- **隐藏（1）：** 权限不在前端菜单中显示，但仍可访问

### 5. 操作列表 (actions)
- 定义权限支持的操作类型
- 常用操作：query（查询）、add（新增）、edit（编辑）、delete（删除）
- 可根据业务需要自定义操作类型

---

**文档更新时间：** 2025-01-30  
**接口版本：** v1.0.0
