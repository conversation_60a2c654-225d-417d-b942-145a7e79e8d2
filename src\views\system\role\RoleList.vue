<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="角色名称">
              <a-input v-model="queryParam.name" placeholder="请输入角色名称"/>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="状态">
              <a-select v-model="queryParam.status" placeholder="请选择" allowClear>
                <a-select-option :value="1">启用</a-select-option>
                <a-select-option :value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="handleAdd" v-action:add>新建角色</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="handleBatchDelete">
            <a-icon type="delete" />删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作 <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <s-table
      ref="table"
      size="default"
      rowKey="id"
      :columns="columns"
      :data="loadData"
      :rowSelection="rowSelection"
      showPagination="auto"
      :scroll="{ x: 1200 }"
    >
      <div
        slot="expandedRowRender"
        slot-scope="record"
        style="margin: 0">
        <a-row
          :gutter="24"
          :style="{ marginBottom: '12px' }">
          <a-col :span="12" v-for="(permission, index) in record.permissions" :key="index" :style="{ marginBottom: '12px' }">
            <a-col :span="4">
              <span>{{ permission.permissionName }}：</span>
            </a-col>
            <a-col :span="20" v-if="permission.actionEntitySet && permission.actionEntitySet.length > 0">
              <a-tag color="cyan" v-for="(action, k) in permission.actionEntitySet" :key="k">{{ action.describe }}</a-tag>
            </a-col>
            <a-col :span="20" v-else>-</a-col>
          </a-col>
        </a-row>
      </div>

      <span slot="status" slot-scope="text">
        <a-badge :status="text === 1 ? 'success' : 'default'" :text="text === 1 ? '启用' : '禁用'" />
      </span>

      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record)" v-action:edit>编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">
              更多 <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item key="1">
                <a @click="handleView(record)">详情</a>
              </a-menu-item>
              <a-menu-item key="2">
                <a @click="handleToggleStatus(record)">
                  {{ record.status === 1 ? '禁用' : '启用' }}
                </a>
              </a-menu-item>
              <a-menu-item key="3">
                <a @click="handleDelete(record)" v-action:delete style="color: #ff4d4f;">删除</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </template>
      </span>
    </s-table>

    <role-modal ref="modal" @ok="handleOk"></role-modal>

  </a-card>
</template>

<script>
import { STable } from '@/components'
import RoleModal from './modules/RoleModal'
import { getRoleList, deleteRole, updateRole } from '@/api/manage'
import { adaptRoleList, adaptPaginationParams } from '@/utils/dataAdapter'

export default {
  name: 'SystemRoleList',
  components: {
    STable,
    RoleModal
  },
  data () {
    return {
      description: '角色管理：基于 RBAC 设计的角色权限控制，支持角色的增删改查和权限分配。',

      // 查询参数
      queryParam: {},
      // 表头
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          fixed: 'left'
        },
        {
          title: '角色编码',
          dataIndex: 'code',
          width: 120
        },
        {
          title: '角色名称',
          dataIndex: 'name',
          width: 150,
          ellipsis: true
        },
        {
          title: '描述',
          dataIndex: 'description',
          width: 200,
          ellipsis: true
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 180,
          sorter: true
        },
        {
          title: '操作',
          width: 150,
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
          fixed: 'right'
        }
      ],
      // 加载数据方法 必须为 Promise 对象
      loadData: parameter => {
        const requestParameters = adaptPaginationParams(Object.assign(parameter, this.queryParam))
        console.log('loadData.requestParameters:', requestParameters)

        return getRoleList(requestParameters).then(res => {
          console.log('getRoleList response:', res)
          return adaptRoleList(res.result)
        }).catch(err => {
          console.error('getRoleList error:', err)
          // 返回空数据作为fallback
          return {
            data: [],
            pageNo: 1,
            pageSize: 10,
            totalCount: 0,
            totalPage: 0
          }
        })
      },

      selectedRowKeys: [],
      selectedRows: []
    }
  },
  computed: {
    rowSelection () {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  methods: {
    // 刷新页面数据
    refresh () {
      return new Promise((resolve, reject) => {
        try {
          this.$refs.table.refresh()
          resolve()
        } catch (error) {
          reject(error)
        }
      })
    },

    // 搜索
    handleSearch () {
      this.$refs.table.refresh(true)
    },

    // 重置搜索
    handleReset () {
      this.queryParam = {}
      this.$refs.table.refresh(true)
    },

    // 新增角色
    handleAdd () {
      this.$refs.modal.add()
    },

    // 编辑角色
    handleEdit (record) {
      this.$refs.modal.edit(record)
    },

    // 查看详情
    handleView (record) {
      this.$refs.modal.view(record)
    },

    // 删除角色
    handleDelete (record) {
      const that = this
      this.$confirm({
        title: '确认删除',
        content: `确定要删除角色 "${record.name}" 吗？`,
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        onOk () {
          return deleteRole(record.id).then(() => {
            that.$message.success('删除成功')
            that.$refs.table.refresh()
          }).catch(err => {
            that.$message.error('删除失败：' + (err.message || '未知错误'))
          })
        }
      })
    },

    // 切换状态
    handleToggleStatus (record) {
      const newStatus = record.status === 1 ? 0 : 1
      const statusText = newStatus === 1 ? '启用' : '禁用'

      this.$confirm({
        title: '确认操作',
        content: `确定要${statusText}角色 "${record.name}" 吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          return updateRole(record.id, { status: newStatus }).then(() => {
            this.$message.success(`${statusText}成功`)
            this.$refs.table.refresh()
          }).catch(err => {
            this.$message.error(`${statusText}失败：` + (err.message || '未知错误'))
          })
        }
      })
    },

    // 批量删除
    handleBatchDelete () {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要删除的角色')
        return
      }

      this.$confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${this.selectedRowKeys.length} 个角色吗？`,
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          const deletePromises = this.selectedRowKeys.map(id => deleteRole(id))
          return Promise.all(deletePromises).then(() => {
            this.$message.success('批量删除成功')
            this.selectedRowKeys = []
            this.selectedRows = []
            this.$refs.table.refresh()
          }).catch(err => {
            this.$message.error('批量删除失败：' + (err.message || '未知错误'))
          })
        }
      })
    },

    // 表格行选择
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },

    // 新增/修改成功回调
    handleOk () {
      this.$refs.table.refresh()
    }
  }
}
</script>

<style scoped>
/* 移动端表格优化 */
@media (max-width: 768px) {
  .table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* 确保表格在小屏幕下可以滚动 */
  :deep(.ant-table-wrapper) {
    overflow-x: auto;
  }

  :deep(.ant-table) {
    min-width: 800px;
  }

  /* 优化操作列的显示 */
  :deep(.ant-table-fixed-right) {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  :deep(.ant-table) {
    font-size: 12px;
  }

  :deep(.ant-btn) {
    padding: 0 8px;
    font-size: 12px;
  }
}
</style>
