export default {
  'account.settings.menuMap.basic': 'Basic Settings',
  'account.settings.menuMap.security': 'Security Settings',
  'account.settings.menuMap.custom': 'Custom Settings',
  'account.settings.menuMap.binding': 'Account Binding',
  'account.settings.menuMap.notification': 'New Message Notification',
  'account.settings.basic.avatar': 'Avatar',
  'account.settings.basic.change-avatar': 'Change avatar',
  'account.settings.basic.email': 'Email',
  'account.settings.basic.email-message': 'Please input your email!',
  'account.settings.basic.nickname': 'Nickname',
  'account.settings.basic.nickname-message': 'Please input your Nickname!',
  'account.settings.basic.profile': 'Personal profile',
  'account.settings.basic.profile-message': 'Please input your personal profile!',
  'account.settings.basic.profile-placeholder': 'Brief introduction to yourself',
  'account.settings.basic.country': 'Country/Region',
  'account.settings.basic.country-message': 'Please input your country!',
  'account.settings.basic.geographic': 'Province or city',
  'account.settings.basic.geographic-message': 'Please input your geographic info!',
  'account.settings.basic.address': 'Street Address',
  'account.settings.basic.address-message': 'Please input your address!',
  'account.settings.basic.phone': 'Phone Number',
  'account.settings.basic.phone-message': 'Please input your phone!',
  'account.settings.basic.update': 'Update Information',
  'account.settings.basic.update.success': 'Update basic information successfully',
  'account.settings.security.strong': 'Strong',
  'account.settings.security.medium': 'Medium',
  'account.settings.security.weak': 'Weak',
  'account.settings.security.password': 'Account Password',
  'account.settings.security.password-description': 'Current password strength：',
  'account.settings.security.phone': 'Security Phone',
  'account.settings.security.phone-description': 'Bound phone：',
  'account.settings.security.question': 'Security Question',
  'account.settings.security.question-description':
    'The security question is not set, and the security policy can effectively protect the account security',
  'account.settings.security.email': 'Backup Email',
  'account.settings.security.email-description': 'Bound Email：',
  'account.settings.security.mfa': 'MFA Device',
  'account.settings.security.mfa-description':
    'Unbound MFA device, after binding, can be confirmed twice',
  'account.settings.security.modify': 'Modify',
  'account.settings.security.set': 'Set',
  'account.settings.security.bind': 'Bind',
  'account.settings.binding.taobao': 'Binding Taobao',
  'account.settings.binding.taobao-description': 'Currently unbound Taobao account',
  'account.settings.binding.alipay': 'Binding Alipay',
  'account.settings.binding.alipay-description': 'Currently unbound Alipay account',
  'account.settings.binding.dingding': 'Binding DingTalk',
  'account.settings.binding.dingding-description': 'Currently unbound DingTalk account',
  'account.settings.binding.bind': 'Bind',
  'account.settings.notification.password': 'Account Password',
  'account.settings.notification.password-description':
    'Messages from other users will be notified in the form of a station letter',
  'account.settings.notification.messages': 'System Messages',
  'account.settings.notification.messages-description':
    'System messages will be notified in the form of a station letter',
  'account.settings.notification.todo': 'To-do Notification',
  'account.settings.notification.todo-description':
    'The to-do list will be notified in the form of a letter from the station',
  'account.settings.settings.open': 'Open',
  'account.settings.settings.close': 'Close'
}
